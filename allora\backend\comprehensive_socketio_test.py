#!/usr/bin/env python3
"""
COMPREHENSIVE SocketIO Feature Test Suite
========================================

This test suite properly tests ALL the actual SocketIO features implemented in the backend:

1. Connection Management & Authentication
2. Event Handlers (connect, disconnect, ping/pong, subscribe, heartbeat)
3. Real-time Broadcasting (inventory, price, cart, order, notifications)
4. Room-based Messaging (user rooms, admin room, guest room, event rooms)
5. Redis Pub/Sub Integration
6. API Integration for triggering events
7. Session tracking and cleanup

Author: Allora Development Team
Date: 2025-07-16
"""

import socketio
import requests
import time
import threading
import json
from datetime import datetime
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveSocketIOTest:
    """Comprehensive test for ALL SocketIO features"""
    
    def __init__(self, server_url="http://localhost:5000"):
        self.server_url = server_url
        self.test_results = {}
        self.clients = {}  # Store different types of clients
        self.received_events = {}  # Track events per client
        
    def log_result(self, test_name: str, success: bool, details: str, duration: float = 0):
        """Log test result"""
        status = "[PASS]" if success else "[FAIL]"
        logger.info(f"{status} {test_name} ({duration:.2f}s): {details}")
        self.test_results[test_name] = {
            'success': success,
            'details': details,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        }
    
    def create_client(self, client_type: str, user_data: Dict = None) -> socketio.Client:
        """Create a SocketIO client with proper event handlers"""
        client = socketio.Client(logger=False, engineio_logger=False)
        
        # Initialize event tracking for this client
        self.received_events[client_type] = []
        
        # Connection event handlers
        @client.event
        def connect():
            logger.info(f"✅ {client_type} connected")
        
        @client.event
        def disconnect():
            logger.info(f"🔌 {client_type} disconnected")
        
        @client.event
        def connect_error(data):
            logger.error(f"❌ {client_type} connection error: {data}")
        
        # Core event handlers
        @client.event
        def connection_established(data):
            self.received_events[client_type].append(('connection_established', data))
            logger.info(f"📡 {client_type} connection established: {data}")
        
        @client.event
        def pong(data):
            self.received_events[client_type].append(('pong', data))
            logger.info(f"🏓 {client_type} pong received: {data}")
        
        @client.event
        def subscribed(data):
            self.received_events[client_type].append(('subscribed', data))
            logger.info(f"🔔 {client_type} subscription confirmed: {data}")
        
        @client.event
        def heartbeat_ack(data):
            self.received_events[client_type].append(('heartbeat_ack', data))
            logger.info(f"💓 {client_type} heartbeat acknowledged: {data}")
        
        # Real-time event handlers
        @client.event
        def inventory_update(data):
            self.received_events[client_type].append(('inventory_update', data))
            logger.info(f"📦 {client_type} inventory update: {data}")
        
        @client.event
        def price_update(data):
            self.received_events[client_type].append(('price_update', data))
            logger.info(f"💰 {client_type} price update: {data}")
        
        @client.event
        def cart_update(data):
            self.received_events[client_type].append(('cart_update', data))
            logger.info(f"🛒 {client_type} cart update: {data}")
        
        @client.event
        def order_update(data):
            self.received_events[client_type].append(('order_update', data))
            logger.info(f"📋 {client_type} order update: {data}")
        
        @client.event
        def notification(data):
            self.received_events[client_type].append(('notification', data))
            logger.info(f"🔔 {client_type} notification: {data}")
        
        @client.event
        def broadcast(data):
            self.received_events[client_type].append(('broadcast', data))
            logger.info(f"📢 {client_type} broadcast: {data}")
        
        @client.event
        def admin_notification(data):
            self.received_events[client_type].append(('admin_notification', data))
            logger.info(f"👑 {client_type} admin notification: {data}")
        
        return client
    
    def test_server_health(self) -> bool:
        """Test basic server health"""
        start_time = time.time()
        try:
            response = requests.get(f"{self.server_url}/api/health", timeout=10)
            success = response.status_code == 200
            details = f"Server healthy (status: {response.status_code})"
        except Exception as e:
            success = False
            details = f"Server not accessible: {e}"
        
        duration = time.time() - start_time
        self.log_result("Server Health", success, details, duration)
        return success
    
    def test_connection_management(self) -> bool:
        """Test 1: Connection Management & Authentication"""
        start_time = time.time()
        logger.info("🔌 Testing Connection Management & Authentication...")
        
        try:
            # Test 1.1: Guest Connection
            guest_client = self.create_client("guest")
            guest_client.connect(self.server_url, wait_timeout=30)
            time.sleep(2)
            
            guest_connected = guest_client.connected
            
            # Test 1.2: Authenticated User Connection
            user_client = self.create_client("user")
            user_auth = {'user_id': 'test_user_123', 'is_admin': False}
            user_client.connect(self.server_url, auth=user_auth, wait_timeout=30)
            time.sleep(2)
            
            user_connected = user_client.connected
            
            # Test 1.3: Admin Connection
            admin_client = self.create_client("admin")
            admin_auth = {'user_id': 'admin_user', 'is_admin': True}
            admin_client.connect(self.server_url, auth=admin_auth, wait_timeout=30)
            time.sleep(2)
            
            admin_connected = admin_client.connected
            
            # Store clients for later tests
            if guest_connected:
                self.clients['guest'] = guest_client
            if user_connected:
                self.clients['user'] = user_client
            if admin_connected:
                self.clients['admin'] = admin_client
            
            success = guest_connected and user_connected and admin_connected
            details = f"Guest: {guest_connected}, User: {user_connected}, Admin: {admin_connected}"
            
        except Exception as e:
            success = False
            details = f"Connection test failed: {e}"
        
        duration = time.time() - start_time
        self.log_result("Connection Management", success, details, duration)
        return success
    
    def test_event_handlers(self) -> bool:
        """Test 2: Core Event Handlers"""
        start_time = time.time()
        logger.info("📨 Testing Core Event Handlers...")
        
        if not self.clients:
            self.log_result("Event Handlers", False, "No connected clients", 0)
            return False
        
        try:
            test_client = list(self.clients.values())[0]
            client_type = list(self.clients.keys())[0]
            initial_events = len(self.received_events[client_type])
            
            # Test ping/pong
            test_client.emit('ping')
            time.sleep(2)
            
            # Test heartbeat
            test_client.emit('heartbeat')
            time.sleep(2)
            
            # Test subscription
            test_client.emit('subscribe', {'events': ['inventory', 'prices', 'orders']})
            time.sleep(2)
            
            # Check received events
            new_events = self.received_events[client_type][initial_events:]
            event_types = [event[0] for event in new_events]
            
            pong_received = 'pong' in event_types
            heartbeat_received = 'heartbeat_ack' in event_types
            subscription_received = 'subscribed' in event_types
            
            success = pong_received and heartbeat_received and subscription_received
            details = f"Pong: {pong_received}, Heartbeat: {heartbeat_received}, Subscribe: {subscription_received}"
            
        except Exception as e:
            success = False
            details = f"Event handler test failed: {e}"
        
        duration = time.time() - start_time
        self.log_result("Event Handlers", success, details, duration)
        return success

    def test_real_time_broadcasting(self) -> bool:
        """Test 3: Real-time Broadcasting Features"""
        start_time = time.time()
        logger.info("📡 Testing Real-time Broadcasting...")

        if not self.clients:
            self.log_result("Real-time Broadcasting", False, "No connected clients", 0)
            return False

        try:
            # Clear previous events
            for client_type in self.received_events:
                self.received_events[client_type].clear()

            # Test 3.1: Inventory Update Broadcast
            inventory_response = requests.post(
                f"{self.server_url}/api/socketio/events/inventory-update",
                json={'product_id': 12345, 'new_quantity': 100, 'old_quantity': 75},
                timeout=10
            )
            time.sleep(2)

            # Test 3.2: Price Update Broadcast
            price_response = requests.post(
                f"{self.server_url}/api/socketio/events/price-update",
                json={'product_id': 12345, 'new_price': 29.99, 'old_price': 39.99},
                timeout=10
            )
            time.sleep(2)

            # Test 3.3: General Broadcast
            broadcast_response = requests.post(
                f"{self.server_url}/api/socketio/broadcast",
                json={'message': 'Test broadcast message', 'type': 'announcement'},
                timeout=10
            )
            time.sleep(2)

            # Check if events were received by clients
            inventory_received = any(
                any(event[0] == 'inventory_update' for event in events)
                for events in self.received_events.values()
            )

            price_received = any(
                any(event[0] == 'price_update' for event in events)
                for events in self.received_events.values()
            )

            broadcast_received = any(
                any(event[0] == 'broadcast' for event in events)
                for events in self.received_events.values()
            )

            api_success = all(r.status_code == 200 for r in [inventory_response, price_response, broadcast_response])

            success = inventory_received and price_received and broadcast_received and api_success
            details = f"Inventory: {inventory_received}, Price: {price_received}, Broadcast: {broadcast_received}, API: {api_success}"

        except Exception as e:
            success = False
            details = f"Broadcasting test failed: {e}"

        duration = time.time() - start_time
        self.log_result("Real-time Broadcasting", success, details, duration)
        return success

    def test_room_based_messaging(self) -> bool:
        """Test 4: Room-based Messaging"""
        start_time = time.time()
        logger.info("🏠 Testing Room-based Messaging...")

        if 'user' not in self.clients or 'admin' not in self.clients:
            self.log_result("Room-based Messaging", False, "Missing user or admin clients", 0)
            return False

        try:
            # Clear previous events
            for client_type in self.received_events:
                self.received_events[client_type].clear()

            # Test 4.1: User-specific notification
            user_notification_response = requests.post(
                f"{self.server_url}/api/socketio/notify-user/test_user_123",
                json={'title': 'Personal Notification', 'message': 'This is for you only'},
                timeout=10
            )
            time.sleep(2)

            # Test 4.2: User-specific cart update
            cart_update_response = requests.post(
                f"{self.server_url}/api/socketio/events/cart-update",
                json={
                    'user_id': 'test_user_123',
                    'cart_data': {'items': [{'product_id': 1, 'quantity': 2}], 'total': 59.98}
                },
                timeout=10
            )
            time.sleep(2)

            # Test 4.3: User-specific order update
            order_update_response = requests.post(
                f"{self.server_url}/api/socketio/events/order-update",
                json={'user_id': 'test_user_123', 'order_id': 'order_789', 'status': 'shipped'},
                timeout=10
            )
            time.sleep(2)

            # Test 4.4: Admin broadcast
            admin_broadcast_response = requests.post(
                f"{self.server_url}/api/socketio/admin/broadcast",
                json={'message': 'Admin only message', 'priority': 'high'},
                timeout=10
            )
            time.sleep(2)

            # Check if user received user-specific events
            user_events = [event[0] for event in self.received_events.get('user', [])]
            user_notification_received = 'notification' in user_events
            user_cart_received = 'cart_update' in user_events
            user_order_received = 'order_update' in user_events

            # Check if admin received admin-specific events
            admin_events = [event[0] for event in self.received_events.get('admin', [])]
            admin_notification_received = 'admin_notification' in admin_events

            # Check if guest did NOT receive user-specific events
            guest_events = [event[0] for event in self.received_events.get('guest', [])]
            guest_isolation = not any(event in guest_events for event in ['notification', 'cart_update', 'order_update'])

            api_success = all(r.status_code == 200 for r in [
                user_notification_response, cart_update_response, order_update_response, admin_broadcast_response
            ])

            success = (user_notification_received and user_cart_received and
                      user_order_received and admin_notification_received and
                      guest_isolation and api_success)

            details = f"User events: {len(user_events)}, Admin events: {len(admin_events)}, Guest isolation: {guest_isolation}, API: {api_success}"

        except Exception as e:
            success = False
            details = f"Room messaging test failed: {e}"

        duration = time.time() - start_time
        self.log_result("Room-based Messaging", success, details, duration)
        return success

    def test_connection_statistics(self) -> bool:
        """Test 5: Connection Statistics API"""
        start_time = time.time()
        logger.info("📊 Testing Connection Statistics...")

        try:
            response = requests.get(f"{self.server_url}/api/socketio/connections", timeout=10)

            if response.status_code == 200:
                stats = response.json()

                # Verify expected fields
                required_fields = ['active_users', 'guest_sessions', 'admin_sessions', 'total_connections']
                has_required_fields = all(field in stats for field in required_fields)

                # Verify reasonable values
                total_connections = stats.get('total_connections', 0)
                active_users = stats.get('active_users', 0)
                guest_sessions = stats.get('guest_sessions', 0)
                admin_sessions = stats.get('admin_sessions', 0)

                reasonable_values = (
                    total_connections >= 0 and
                    active_users >= 0 and
                    guest_sessions >= 0 and
                    admin_sessions >= 0 and
                    total_connections >= len(self.clients)
                )

                success = has_required_fields and reasonable_values
                details = f"Connections: {total_connections}, Users: {active_users}, Guests: {guest_sessions}, Admins: {admin_sessions}"
            else:
                success = False
                details = f"API returned status {response.status_code}"

        except Exception as e:
            success = False
            details = f"Connection stats test failed: {e}"

        duration = time.time() - start_time
        self.log_result("Connection Statistics", success, details, duration)
        return success

    def test_redis_integration(self) -> bool:
        """Test 6: Redis Pub/Sub Integration"""
        start_time = time.time()
        logger.info("🔴 Testing Redis Pub/Sub Integration...")

        try:
            # Clear previous events
            for client_type in self.received_events:
                self.received_events[client_type].clear()

            # Send multiple events that should use Redis pub/sub
            events_sent = 0

            # Send inventory update (uses Redis)
            response1 = requests.post(
                f"{self.server_url}/api/socketio/events/inventory-update",
                json={'product_id': 99999, 'new_quantity': 50, 'old_quantity': 25},
                timeout=10
            )
            if response1.status_code == 200:
                events_sent += 1

            # Send price update (uses Redis)
            response2 = requests.post(
                f"{self.server_url}/api/socketio/events/price-update",
                json={'product_id': 99999, 'new_price': 19.99, 'old_price': 24.99},
                timeout=10
            )
            if response2.status_code == 200:
                events_sent += 1

            time.sleep(3)  # Wait for Redis pub/sub processing

            # Check if events were received (indicating Redis is working)
            total_events_received = sum(len(events) for events in self.received_events.values())

            success = events_sent > 0 and total_events_received > 0
            details = f"Events sent: {events_sent}, Events received: {total_events_received}"

        except Exception as e:
            success = False
            details = f"Redis integration test failed: {e}"

        duration = time.time() - start_time
        self.log_result("Redis Integration", success, details, duration)
        return success

    def test_concurrent_connections(self) -> bool:
        """Test 7: Concurrent Connections & Performance"""
        start_time = time.time()
        logger.info("⚡ Testing Concurrent Connections & Performance...")

        try:
            concurrent_clients = []
            connection_results = []

            def create_concurrent_client(client_id):
                try:
                    client = self.create_client(f"concurrent_{client_id}")
                    auth = {'user_id': f'concurrent_user_{client_id}', 'is_admin': False}
                    client.connect(self.server_url, auth=auth, wait_timeout=30)
                    time.sleep(1)

                    if client.connected:
                        concurrent_clients.append(client)
                        connection_results.append(True)
                    else:
                        connection_results.append(False)

                except Exception as e:
                    logger.error(f"Concurrent client {client_id} failed: {e}")
                    connection_results.append(False)

            # Create 5 concurrent connections
            threads = []
            for i in range(5):
                thread = threading.Thread(target=create_concurrent_client, args=(i,))
                threads.append(thread)
                thread.start()

            # Wait for all connections
            for thread in threads:
                thread.join()

            time.sleep(2)  # Let connections stabilize

            # Test concurrent event sending
            if concurrent_clients:
                # Send events to all concurrent clients
                broadcast_response = requests.post(
                    f"{self.server_url}/api/socketio/broadcast",
                    json={'message': 'Concurrent test broadcast', 'type': 'performance_test'},
                    timeout=10
                )

                time.sleep(2)

                # Check if events were received
                concurrent_events_received = sum(
                    len(self.received_events.get(f"concurrent_{i}", []))
                    for i in range(5)
                )
            else:
                concurrent_events_received = 0
                broadcast_response = None

            # Clean up concurrent clients
            for client in concurrent_clients:
                try:
                    client.disconnect()
                except:
                    pass

            successful_connections = sum(connection_results)
            success = successful_connections >= 3  # At least 60% success rate
            details = f"Successful connections: {successful_connections}/5, Events received: {concurrent_events_received}"

        except Exception as e:
            success = False
            details = f"Concurrent connections test failed: {e}"

        duration = time.time() - start_time
        self.log_result("Concurrent Connections", success, details, duration)
        return success

    def test_session_cleanup(self) -> bool:
        """Test 8: Session Cleanup & Disconnection"""
        start_time = time.time()
        logger.info("🧹 Testing Session Cleanup & Disconnection...")

        try:
            # Create a temporary client
            temp_client = self.create_client("temp_cleanup")
            temp_auth = {'user_id': 'temp_cleanup_user', 'is_admin': False}
            temp_client.connect(self.server_url, auth=temp_auth, wait_timeout=30)
            time.sleep(1)

            if not temp_client.connected:
                success = False
                details = "Failed to create temporary client for cleanup test"
            else:
                # Get initial connection stats
                stats_response = requests.get(f"{self.server_url}/api/socketio/connections", timeout=10)
                initial_connections = stats_response.json().get('total_connections', 0) if stats_response.status_code == 200 else 0

                # Disconnect the client
                temp_client.disconnect()
                time.sleep(2)  # Wait for cleanup

                # Get final connection stats
                stats_response = requests.get(f"{self.server_url}/api/socketio/connections", timeout=10)
                final_connections = stats_response.json().get('total_connections', 0) if stats_response.status_code == 200 else 0

                # Verify cleanup occurred
                cleanup_successful = final_connections < initial_connections

                success = cleanup_successful
                details = f"Connections before: {initial_connections}, after: {final_connections}, cleanup: {cleanup_successful}"

        except Exception as e:
            success = False
            details = f"Session cleanup test failed: {e}"

        duration = time.time() - start_time
        self.log_result("Session Cleanup", success, details, duration)
        return success

    def cleanup_all_clients(self):
        """Clean up all test clients"""
        logger.info("🧹 Cleaning up all test clients...")

        for client_type, client in self.clients.items():
            try:
                if client.connected:
                    client.disconnect()
                    logger.info(f"✅ Disconnected {client_type} client")
            except Exception as e:
                logger.error(f"❌ Error disconnecting {client_type} client: {e}")

        self.clients.clear()
        self.received_events.clear()
        logger.info("✅ All clients cleaned up")

    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        # Calculate total events received across all tests
        total_events = sum(len(events) for events in self.received_events.values())

        report = {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': f"{success_rate:.1f}%",
                'total_duration': sum(result['duration'] for result in self.test_results.values()),
                'total_events_received': total_events
            },
            'test_results': self.test_results,
            'events_summary': {
                client_type: len(events) for client_type, events in self.received_events.items()
            },
            'timestamp': datetime.now().isoformat()
        }

        return report

    def run_all_tests(self) -> bool:
        """Run all comprehensive SocketIO tests"""
        logger.info("🚀 Starting COMPREHENSIVE SocketIO Feature Test Suite")
        logger.info("=" * 80)
        logger.info(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🎯 Target: {self.server_url}")
        logger.info("=" * 80)

        try:
            # Phase 1: Basic Setup
            logger.info("\n📡 Phase 1: Server Health & Setup")
            if not self.test_server_health():
                logger.error("❌ Server not available - aborting tests")
                return False

            # Phase 2: Connection Management
            logger.info("\n🔌 Phase 2: Connection Management & Authentication")
            if not self.test_connection_management():
                logger.error("❌ Connection management failed - aborting tests")
                return False

            # Phase 3: Core Event Handlers
            logger.info("\n📨 Phase 3: Core Event Handlers")
            self.test_event_handlers()

            # Phase 4: Real-time Broadcasting
            logger.info("\n📡 Phase 4: Real-time Broadcasting")
            self.test_real_time_broadcasting()

            # Phase 5: Room-based Messaging
            logger.info("\n🏠 Phase 5: Room-based Messaging")
            self.test_room_based_messaging()

            # Phase 6: Connection Statistics
            logger.info("\n📊 Phase 6: Connection Statistics")
            self.test_connection_statistics()

            # Phase 7: Redis Integration
            logger.info("\n🔴 Phase 7: Redis Pub/Sub Integration")
            self.test_redis_integration()

            # Phase 8: Performance & Concurrency
            logger.info("\n⚡ Phase 8: Performance & Concurrency")
            self.test_concurrent_connections()

            # Phase 9: Session Cleanup
            logger.info("\n🧹 Phase 9: Session Cleanup")
            self.test_session_cleanup()

            return True

        except KeyboardInterrupt:
            logger.info("\n🛑 Tests interrupted by user")
            return False
        except Exception as e:
            logger.error(f"\n❌ Test suite error: {e}")
            return False
        finally:
            self.cleanup_all_clients()

    def print_final_report(self):
        """Print comprehensive final test report"""
        report = self.generate_comprehensive_report()

        logger.info("\n" + "=" * 80)
        logger.info("📊 COMPREHENSIVE SOCKETIO FEATURE TEST REPORT")
        logger.info("=" * 80)

        # Summary
        summary = report['summary']
        logger.info(f"📈 Total Tests: {summary['total_tests']}")
        logger.info(f"✅ Passed: {summary['passed_tests']}")
        logger.info(f"❌ Failed: {summary['failed_tests']}")
        logger.info(f"📊 Success Rate: {summary['success_rate']}")
        logger.info(f"⏱️  Total Duration: {summary['total_duration']:.2f}s")
        logger.info(f"📨 Total Events Received: {summary['total_events_received']}")

        # Events breakdown
        events_summary = report['events_summary']
        if events_summary:
            logger.info(f"\n📨 Events by Client Type:")
            for client_type, event_count in events_summary.items():
                logger.info(f"   {client_type}: {event_count} events")

        # Failed tests details
        failed_tests = [name for name, result in report['test_results'].items() if not result['success']]
        if failed_tests:
            logger.info(f"\n❌ Failed Tests:")
            for test_name in failed_tests:
                result = report['test_results'][test_name]
                logger.info(f"   - {test_name}: {result['details']}")

        # Overall assessment
        success_rate = float(summary['success_rate'].rstrip('%'))
        logger.info(f"\n🎯 Overall Assessment:")
        if success_rate >= 90:
            logger.info("✅ EXCELLENT: All SocketIO features working perfectly!")
        elif success_rate >= 75:
            logger.info("✅ GOOD: SocketIO features working well with minor issues")
        elif success_rate >= 50:
            logger.info("⚠️ FAIR: SocketIO features have some issues that need attention")
        else:
            logger.info("❌ POOR: SocketIO features have significant issues")

        # Save detailed report
        try:
            with open('comprehensive_socketio_test_report.json', 'w') as f:
                json.dump(report, f, indent=2)
            logger.info(f"\n💾 Detailed report saved to: comprehensive_socketio_test_report.json")
        except Exception as e:
            logger.error(f"Failed to save report: {e}")

        logger.info("=" * 80)


def main():
    """Main test execution function"""
    print("🧪 COMPREHENSIVE SocketIO Feature Test Suite")
    print("=" * 60)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)

    # Create test suite
    test_suite = ComprehensiveSocketIOTest()

    try:
        # Run all tests
        success = test_suite.run_all_tests()

        # Print final report
        test_suite.print_final_report()

        # Exit with appropriate code
        if success:
            print("\n🎉 Comprehensive SocketIO test suite completed!")
        else:
            print("\n⚠️ Some tests failed. Check the report above.")

        return 0 if success else 1

    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        test_suite.cleanup_all_clients()
        return 1


if __name__ == "__main__":
    exit(main())
