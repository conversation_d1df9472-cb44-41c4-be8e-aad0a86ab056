{"summary": {"total_tests": 13, "passed_tests": 12, "failed_tests": 1, "success_rate": "92.3%", "production_ready": true}, "test_results": {"Server Health": {"success": true, "details": "Server healthy - <PERSON><PERSON> backend is running", "duration": 2.0783205032348633, "timestamp": "2025-07-16T17:04:41.367085"}, "SocketIO Test Page": {"success": true, "details": "SocketIO test page available (SocketIO: True, Client: False)", "duration": 2.1022963523864746, "timestamp": "2025-07-16T17:04:43.469382"}, "SocketIO Handshake": {"success": true, "details": "Handshake successful (SID: True, Upgrades: True, PingTimeout: True)", "duration": 27.048688888549805, "timestamp": "2025-07-16T17:05:10.519086"}, "Connection Statistics API": {"success": true, "details": "Stats API working - Total: 0, Users: 0", "duration": 2.086771011352539, "timestamp": "2025-07-16T17:05:12.605857"}, "Broadcast API": {"success": true, "details": "Broadcast API working - Message sent successfully", "duration": 2.086554527282715, "timestamp": "2025-07-16T17:05:14.694405"}, "User Notification API": {"success": true, "details": "User notification API working - Notification sent successfully", "duration": 2.0752127170562744, "timestamp": "2025-07-16T17:05:16.771456"}, "Inventory Update API": {"success": true, "details": "Inventory update API working - Update broadcasted successfully", "duration": 2.0707263946533203, "timestamp": "2025-07-16T17:05:18.842890"}, "Price Update API": {"success": true, "details": "Price update API working - Update broadcasted successfully", "duration": 2.058120012283325, "timestamp": "2025-07-16T17:05:20.902163"}, "Cart Update API": {"success": true, "details": "Cart update API working - Update sent to user successfully", "duration": 2.0895090103149414, "timestamp": "2025-07-16T17:05:22.991672"}, "Order Update API": {"success": true, "details": "Order update API working - Update sent to user successfully", "duration": 2.083052635192871, "timestamp": "2025-07-16T17:05:25.076697"}, "Admin Broadcast API": {"success": true, "details": "Admin broadcast API working - Message sent to admins successfully", "duration": 2.056547164916992, "timestamp": "2025-07-16T17:05:27.135229"}, "Concurrent API Performance": {"success": true, "details": "Concurrent performance: 5/5 calls successful", "duration": 2.1224851608276367, "timestamp": "2025-07-16T17:05:29.258714"}, "Redis Integration": {"success": false, "details": "Redis integration: 2/3 events processed successfully", "duration": 6.2683494091033936, "timestamp": "2025-07-16T17:05:35.527063"}}, "timestamp": "2025-07-16T17:05:35.538955"}