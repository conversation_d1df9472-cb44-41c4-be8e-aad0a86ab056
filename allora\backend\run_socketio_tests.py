#!/usr/bin/env python3
"""
SocketIO Test Runner
===================

Simple script to run the comprehensive SocketIO tests with proper setup.
This script ensures the server is running and dependencies are available.

Usage:
    python run_socketio_tests.py [server_url]

Examples:
    python run_socketio_tests.py
    python run_socketio_tests.py http://localhost:5000
    python run_socketio_tests.py http://*************:5000

Author: Allora Development Team
Date: 2025-07-16
"""

import sys
import os
import subprocess
import time
import requests
from datetime import datetime

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'requests',
        'python-socketio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   • {package}")
        print("\n💡 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required dependencies are installed")
    return True

def check_server_status(server_url):
    """Check if the server is running and accessible"""
    print(f"🔍 Checking server status at {server_url}...")
    
    try:
        response = requests.get(f"{server_url}/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"⚠️  Server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not accessible (connection refused)")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server request timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False

def start_server_if_needed():
    """Attempt to start the server if it's not running"""
    print("🚀 Attempting to start the server...")
    
    # Check if we're in the backend directory
    if not os.path.exists('app.py'):
        print("❌ app.py not found. Please run this script from the backend directory.")
        return False
    
    try:
        # Try to start the server using the hybrid runner
        if os.path.exists('run_hybrid_server.py'):
            print("🔧 Starting server with run_hybrid_server.py...")
            subprocess.Popen([sys.executable, 'run_hybrid_server.py'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
        elif os.path.exists('run_with_waitress.py'):
            print("🔧 Starting server with run_with_waitress.py...")
            subprocess.Popen([sys.executable, 'run_with_waitress.py'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
        else:
            print("🔧 Starting server with app.py...")
            subprocess.Popen([sys.executable, 'app.py'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        for i in range(30):  # Wait up to 30 seconds
            time.sleep(1)
            if check_server_status("http://localhost:5000"):
                return True
            print(f"   Waiting... ({i+1}/30)")
        
        print("❌ Server failed to start within 30 seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

def main():
    """Main test runner function"""
    print("🧪 SocketIO Test Runner")
    print("=" * 40)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 40)
    
    # Get server URL from command line or use default
    server_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:5000"
    
    print(f"🎯 Target Server: {server_url}")
    
    # Step 1: Check dependencies
    print("\n📦 Step 1: Checking Dependencies")
    if not check_dependencies():
        sys.exit(1)
    
    # Step 2: Check server status
    print("\n🌐 Step 2: Checking Server Status")
    server_running = check_server_status(server_url)
    
    if not server_running and server_url == "http://localhost:5000":
        print("\n🚀 Step 3: Starting Local Server")
        if not start_server_if_needed():
            print("❌ Failed to start server. Please start it manually and try again.")
            sys.exit(1)
    elif not server_running:
        print(f"❌ Server at {server_url} is not accessible.")
        print("💡 Please ensure the server is running and try again.")
        sys.exit(1)
    
    # Step 3: Run the comprehensive tests
    print("\n🧪 Step 4: Running Comprehensive SocketIO Tests")
    print("-" * 40)
    
    try:
        # Set environment variable for the test script
        os.environ['SOCKETIO_TEST_SERVER'] = server_url
        
        # Import and run the test suite
        from test_socketio_comprehensive import main as run_tests
        run_tests()
        
    except ImportError as e:
        print(f"❌ Failed to import test suite: {e}")
        print("💡 Make sure test_socketio_comprehensive.py is in the same directory")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
