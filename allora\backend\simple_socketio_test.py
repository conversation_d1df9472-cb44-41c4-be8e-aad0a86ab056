#!/usr/bin/env python3
"""
Simple SocketIO Connection Test
==============================

A basic test to verify SocketIO connectivity and functionality.
"""

import socketio
import time
import requests

def test_server_health():
    """Test if the server is running"""
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        print(f"✅ Server Health: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Server Health Failed: {e}")
        return False

def test_socketio_page():
    """Test if SocketIO test page is accessible"""
    try:
        response = requests.get('http://localhost:5000/socketio-test', timeout=5)
        print(f"✅ SocketIO Test Page: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ SocketIO Test Page Failed: {e}")
        return False

def test_simple_connection():
    """Test basic SocketIO connection"""
    print("🔌 Testing SocketIO Connection...")

    # Create a simple client with increased HTTP timeout
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # Create client with custom HTTP session that has longer timeout
    import requests
    session = requests.Session()
    session.timeout = 30  # 30 second timeout

    sio = socketio.Client(
        logger=True,
        engineio_logger=True,
        http_session=session
    )

    connected = False
    connection_error = None
    events_received = []

    @sio.event
    def connect():
        nonlocal connected
        connected = True
        print("✅ Connected to SocketIO server!")

    @sio.event
    def disconnect():
        print("🔌 Disconnected from SocketIO server")

    @sio.event
    def connect_error(data):
        nonlocal connection_error
        connection_error = data
        print(f"❌ Connection error: {data}")

    @sio.event
    def pong(data):
        events_received.append(('pong', data))
        print(f"✅ Pong received: {data}")

    @sio.event
    def connection_established(data):
        events_received.append(('connection_established', data))
        print(f"✅ Connection established: {data}")

    try:
        # Try to connect with longer timeout
        print("Attempting connection with 30s timeout and custom HTTP session...")
        sio.connect('http://localhost:5000', wait_timeout=30)

        # Wait a moment for connection to stabilize
        print("Waiting for connection to stabilize...")
        time.sleep(3)

        if connected:
            print("✅ SocketIO connection successful!")

            # Test ping-pong
            print("🏓 Testing ping-pong...")

            # Send ping
            sio.emit('ping')
            print("Ping sent, waiting for pong...")
            time.sleep(5)

            pong_received = any(event[0] == 'pong' for event in events_received)

            if pong_received:
                print("✅ Ping-pong test successful!")
            else:
                print("❌ Ping-pong test failed - no pong received")
                print(f"Events received: {events_received}")

            # Test subscription
            print("🔔 Testing subscription...")
            sio.emit('subscribe', {'events': ['inventory', 'prices']})
            time.sleep(2)

            # Disconnect
            sio.disconnect()
            return True
        else:
            print(f"❌ Failed to connect. Error: {connection_error}")
            return False

    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        try:
            sio.disconnect()
        except:
            pass
        return False

def test_api_endpoints():
    """Test SocketIO API endpoints"""
    print("🌐 Testing SocketIO API endpoints...")
    
    # Test broadcast endpoint
    try:
        response = requests.post(
            'http://localhost:5000/api/socketio/broadcast',
            json={'message': 'Test broadcast', 'type': 'test'},
            timeout=5
        )
        print(f"✅ Broadcast API: {response.status_code}")
    except Exception as e:
        print(f"❌ Broadcast API Failed: {e}")
    
    # Test user notification endpoint
    try:
        response = requests.post(
            'http://localhost:5000/api/socketio/notify-user/test_user',
            json={'title': 'Test', 'message': 'Test notification'},
            timeout=5
        )
        print(f"✅ User Notification API: {response.status_code}")
    except Exception as e:
        print(f"❌ User Notification API Failed: {e}")

def main():
    """Run all simple tests"""
    print("🧪 Simple SocketIO Test Suite")
    print("=" * 40)
    
    # Test 1: Server health
    if not test_server_health():
        print("❌ Server not available - aborting tests")
        return
    
    # Test 2: SocketIO test page
    test_socketio_page()
    
    # Test 3: SocketIO connection
    if test_simple_connection():
        print("✅ SocketIO connection working!")
    else:
        print("❌ SocketIO connection failed!")
    
    # Test 4: API endpoints
    test_api_endpoints()
    
    print("\n🎯 Simple test suite completed!")

if __name__ == "__main__":
    main()
