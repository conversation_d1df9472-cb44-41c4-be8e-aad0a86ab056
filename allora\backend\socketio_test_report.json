{"summary": {"total_tests": 3, "passed_tests": 2, "failed_tests": 1, "success_rate": "66.7%", "total_duration": 11.262090682983398}, "connection_stats": {"successful_connections": 0, "failed_connections": 1, "disconnections": 0, "reconnections": 0}, "test_results": {"Server Availability": {"success": true, "details": "Server response: 200 - {'cors_enabled': True, 'message': '<PERSON>ora backend is running', 'status': 'healthy', 'timestamp': '2025-07-16T11:11:01.242288', 'version': '1.0.0'}", "duration": 2.103233575820923, "timestamp": "2025-07-16T16:41:01.250261"}, "SocketIO Test Page": {"success": true, "details": "SocketIO test page: 200", "duration": 2.075495481491089, "timestamp": "2025-07-16T16:41:03.325756"}, "Basic Connection": {"success": false, "details": "Connection error: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)", "duration": 7.083361625671387, "timestamp": "2025-07-16T16:41:10.411435"}}, "events_received": 0, "timestamp": "2025-07-16T16:41:10.412233"}