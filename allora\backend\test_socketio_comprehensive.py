#!/usr/bin/env python3
"""
Comprehensive SocketIO Integration Test Suite
============================================

This script thoroughly tests all aspects of the Flask-SocketIO implementation:
1. Connection testing (authenticated and guest users)
2. Event handler testing (all custom events)
3. Room management and broadcasting
4. Real-time features (inventory, cart, orders, notifications)
5. API endpoint testing
6. Error handling and edge cases
7. Performance and stress testing
8. Redis pub/sub testing (if available)

Author: Allora Development Team
Date: 2025-07-16
"""

import asyncio
import json
import time
import threading
import requests
import socketio
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import signal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('socketio_test_results.log')
    ]
)
logger = logging.getLogger(__name__)

class SocketIOTestSuite:
    """Comprehensive SocketIO test suite"""
    
    def __init__(self, server_url: str = "http://localhost:5000"):
        self.server_url = server_url
        self.test_results = {}
        self.clients = []
        self.test_data = {
            'users': [
                {'user_id': 'test_user_1', 'is_admin': False},
                {'user_id': 'test_user_2', 'is_admin': False},
                {'user_id': 'admin_user', 'is_admin': True},
                {'user_id': None, 'is_admin': False}  # Guest user
            ],
            'products': [1, 2, 3, 4, 5],
            'orders': ['order_123', 'order_456', 'order_789']
        }
        self.received_events = []
        self.connection_stats = {
            'successful_connections': 0,
            'failed_connections': 0,
            'disconnections': 0,
            'reconnections': 0
        }
        
    def log_test_result(self, test_name: str, success: bool, details: str = "", duration: float = 0):
        """Log test result with details"""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name} ({duration:.2f}s): {details}")
        
        self.test_results[test_name] = {
            'success': success,
            'details': details,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        }
    
    def create_test_client(self, user_data: Dict[str, Any]) -> socketio.SimpleClient:
        """Create a SocketIO test client with authentication"""
        client = socketio.SimpleClient(logger=False, engineio_logger=False)
        
        # Store received events for this client
        client.received_events = []
        
        # Event handlers for testing
        @client.event
        def connect():
            self.connection_stats['successful_connections'] += 1
            logger.info(f"Client connected: {user_data}")
        
        @client.event
        def disconnect():
            self.connection_stats['disconnections'] += 1
            logger.info(f"Client disconnected: {user_data}")
        
        @client.event
        def connect_error(data):
            self.connection_stats['failed_connections'] += 1
            logger.error(f"Connection error for {user_data}: {data}")
        
        # Custom event handlers
        @client.event
        def connection_established(data):
            client.received_events.append(('connection_established', data))
            self.received_events.append(('connection_established', data, user_data))
        
        @client.event
        def pong(data):
            client.received_events.append(('pong', data))
            self.received_events.append(('pong', data, user_data))
        
        @client.event
        def inventory_update(data):
            client.received_events.append(('inventory_update', data))
            self.received_events.append(('inventory_update', data, user_data))
        
        @client.event
        def price_update(data):
            client.received_events.append(('price_update', data))
            self.received_events.append(('price_update', data, user_data))
        
        @client.event
        def cart_update(data):
            client.received_events.append(('cart_update', data))
            self.received_events.append(('cart_update', data, user_data))
        
        @client.event
        def order_status_update(data):
            client.received_events.append(('order_status_update', data))
            self.received_events.append(('order_status_update', data, user_data))
        
        @client.event
        def notification(data):
            client.received_events.append(('notification', data))
            self.received_events.append(('notification', data, user_data))
        
        @client.event
        def broadcast(data):
            client.received_events.append(('broadcast', data))
            self.received_events.append(('broadcast', data, user_data))
        
        @client.event
        def admin_notification(data):
            client.received_events.append(('admin_notification', data))
            self.received_events.append(('admin_notification', data, user_data))
        
        @client.event
        def subscribed(data):
            client.received_events.append(('subscribed', data))
            self.received_events.append(('subscribed', data, user_data))
        
        @client.event
        def heartbeat_ack(data):
            client.received_events.append(('heartbeat_ack', data))
            self.received_events.append(('heartbeat_ack', data, user_data))
        
        return client
    
    def test_server_availability(self) -> bool:
        """Test if the server is running and accessible"""
        start_time = time.time()
        try:
            response = requests.get(f"{self.server_url}/api/health", timeout=10)
            success = response.status_code == 200
            details = f"Server response: {response.status_code}"
            if success:
                details += f" - {response.json()}"
        except Exception as e:
            success = False
            details = f"Server not accessible: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test_result("Server Availability", success, details, duration)
        return success
    
    def test_socketio_endpoint_availability(self) -> bool:
        """Test if SocketIO test page is available"""
        start_time = time.time()
        try:
            response = requests.get(f"{self.server_url}/socketio-test", timeout=10)
            success = response.status_code == 200
            details = f"SocketIO test page: {response.status_code}"
        except Exception as e:
            success = False
            details = f"SocketIO test page not accessible: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test_result("SocketIO Test Page", success, details, duration)
        return success

    def test_basic_connection(self) -> bool:
        """Test basic SocketIO connection without authentication"""
        start_time = time.time()
        try:
            client = self.create_test_client({'user_id': None, 'is_admin': False})
            client.connect(self.server_url, wait_timeout=10)

            # Wait a moment for connection to establish
            time.sleep(1)

            success = client.connected
            details = "Guest connection successful" if success else "Guest connection failed"

            if success:
                client.disconnect()
                self.clients.append(client)

        except Exception as e:
            success = False
            details = f"Connection error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Basic Connection", success, details, duration)
        return success

    def test_authenticated_connections(self) -> bool:
        """Test authenticated user connections"""
        start_time = time.time()
        successful_connections = 0
        total_connections = 0

        for user_data in self.test_data['users']:
            if user_data['user_id'] is None:
                continue  # Skip guest user for this test

            try:
                client = self.create_test_client(user_data)
                auth_data = {
                    'user_id': user_data['user_id'],
                    'is_admin': user_data['is_admin']
                }

                client.connect(self.server_url, auth=auth_data, wait_timeout=10)
                time.sleep(1)

                if client.connected:
                    successful_connections += 1
                    self.clients.append(client)
                    logger.info(f"✅ Connected user: {user_data['user_id']}")
                else:
                    logger.error(f"❌ Failed to connect user: {user_data['user_id']}")

                total_connections += 1

            except Exception as e:
                logger.error(f"❌ Connection error for {user_data['user_id']}: {e}")
                total_connections += 1

        success = successful_connections == total_connections and successful_connections > 0
        details = f"{successful_connections}/{total_connections} authenticated connections successful"

        duration = time.time() - start_time
        self.log_test_result("Authenticated Connections", success, details, duration)
        return success

    def test_ping_pong(self) -> bool:
        """Test ping-pong keepalive functionality"""
        start_time = time.time()

        if not self.clients:
            self.log_test_result("Ping-Pong Test", False, "No connected clients", 0)
            return False

        try:
            client = self.clients[0]
            initial_events = len(client.received_events)

            # Send ping
            client.emit('ping')
            time.sleep(2)  # Wait for pong response

            # Check if pong was received
            pong_received = any(event[0] == 'pong' for event in client.received_events[initial_events:])

            success = pong_received
            details = "Pong received" if success else "Pong not received"

        except Exception as e:
            success = False
            details = f"Ping-pong error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Ping-Pong Test", success, details, duration)
        return success

    def test_subscription_events(self) -> bool:
        """Test event subscription functionality"""
        start_time = time.time()

        if not self.clients:
            self.log_test_result("Subscription Test", False, "No connected clients", 0)
            return False

        try:
            client = self.clients[0]
            initial_events = len(client.received_events)

            # Subscribe to events
            subscription_data = {
                'events': ['inventory', 'prices', 'orders']
            }
            client.emit('subscribe', subscription_data)
            time.sleep(2)  # Wait for subscription confirmation

            # Check if subscription confirmation was received
            subscribed = any(event[0] == 'subscribed' for event in client.received_events[initial_events:])

            success = subscribed
            details = "Subscription confirmed" if success else "Subscription not confirmed"

        except Exception as e:
            success = False
            details = f"Subscription error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Subscription Test", success, details, duration)
        return success

    def test_heartbeat(self) -> bool:
        """Test heartbeat functionality"""
        start_time = time.time()

        if not self.clients:
            self.log_test_result("Heartbeat Test", False, "No connected clients", 0)
            return False

        try:
            client = self.clients[0]
            initial_events = len(client.received_events)

            # Send heartbeat
            client.emit('heartbeat')
            time.sleep(2)  # Wait for heartbeat acknowledgment

            # Check if heartbeat_ack was received
            heartbeat_ack = any(event[0] == 'heartbeat_ack' for event in client.received_events[initial_events:])

            success = heartbeat_ack
            details = "Heartbeat acknowledged" if success else "Heartbeat not acknowledged"

        except Exception as e:
            success = False
            details = f"Heartbeat error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Heartbeat Test", success, details, duration)
        return success

    def test_api_broadcast(self) -> bool:
        """Test API endpoint for broadcasting messages"""
        start_time = time.time()

        if not self.clients:
            self.log_test_result("API Broadcast Test", False, "No connected clients", 0)
            return False

        try:
            # Clear previous events
            for client in self.clients:
                client.received_events.clear()

            # Send broadcast via API
            broadcast_data = {
                'message': 'Test broadcast message from API',
                'type': 'test'
            }

            response = requests.post(
                f"{self.server_url}/api/socketio/broadcast",
                json=broadcast_data,
                timeout=10
            )

            api_success = response.status_code == 200

            if api_success:
                time.sleep(2)  # Wait for broadcast to be received

                # Check if any client received the broadcast
                broadcast_received = False
                for client in self.clients:
                    if any(event[0] == 'broadcast' for event in client.received_events):
                        broadcast_received = True
                        break

                success = broadcast_received
                details = f"API response: {response.status_code}, Broadcast received: {broadcast_received}"
            else:
                success = False
                details = f"API request failed: {response.status_code}"

        except Exception as e:
            success = False
            details = f"API broadcast error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("API Broadcast Test", success, details, duration)
        return success

    def test_api_user_notification(self) -> bool:
        """Test API endpoint for user-specific notifications"""
        start_time = time.time()

        # Find a connected authenticated user
        target_client = None
        target_user_id = None

        for client in self.clients:
            # Check if this client was created with a user_id
            for user_data in self.test_data['users']:
                if user_data['user_id'] and client.connected:
                    target_client = client
                    target_user_id = user_data['user_id']
                    break
            if target_client:
                break

        if not target_client or not target_user_id:
            self.log_test_result("API User Notification Test", False, "No authenticated user connected", 0)
            return False

        try:
            # Clear previous events
            target_client.received_events.clear()

            # Send notification via API
            notification_data = {
                'title': 'Test Notification',
                'message': 'This is a test notification from API',
                'type': 'info'
            }

            response = requests.post(
                f"{self.server_url}/api/socketio/notify-user/{target_user_id}",
                json=notification_data,
                timeout=10
            )

            api_success = response.status_code == 200

            if api_success:
                time.sleep(2)  # Wait for notification to be received

                # Check if the target client received the notification
                notification_received = any(event[0] == 'notification' for event in target_client.received_events)

                success = notification_received
                details = f"API response: {response.status_code}, Notification received: {notification_received}"
            else:
                success = False
                details = f"API request failed: {response.status_code}"

        except Exception as e:
            success = False
            details = f"API notification error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("API User Notification Test", success, details, duration)
        return success

    def test_api_inventory_update(self) -> bool:
        """Test API endpoint for inventory updates"""
        start_time = time.time()

        if not self.clients:
            self.log_test_result("API Inventory Update Test", False, "No connected clients", 0)
            return False

        try:
            # Clear previous events
            for client in self.clients:
                client.received_events.clear()

            # Send inventory update via API
            inventory_data = {
                'product_id': self.test_data['products'][0],
                'new_quantity': 50,
                'old_quantity': 30
            }

            response = requests.post(
                f"{self.server_url}/api/socketio/events/inventory-update",
                json=inventory_data,
                timeout=10
            )

            api_success = response.status_code == 200

            if api_success:
                time.sleep(2)  # Wait for update to be received

                # Check if any client received the inventory update
                update_received = False
                for client in self.clients:
                    if any(event[0] == 'inventory_update' for event in client.received_events):
                        update_received = True
                        break

                success = update_received
                details = f"API response: {response.status_code}, Update received: {update_received}"
            else:
                success = False
                details = f"API request failed: {response.status_code}"

        except Exception as e:
            success = False
            details = f"API inventory update error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("API Inventory Update Test", success, details, duration)
        return success

    def test_api_cart_update(self) -> bool:
        """Test API endpoint for cart updates"""
        start_time = time.time()

        # Find a connected user
        target_user_id = None
        for user_data in self.test_data['users']:
            if user_data['user_id']:
                target_user_id = user_data['user_id']
                break

        if not target_user_id:
            self.log_test_result("API Cart Update Test", False, "No user ID available", 0)
            return False

        try:
            # Clear previous events
            for client in self.clients:
                client.received_events.clear()

            # Send cart update via API
            cart_data = {
                'user_id': target_user_id,
                'session_id': 'test_session_123',
                'cart_data': {
                    'items': [
                        {'product_id': 1, 'quantity': 2, 'price': 29.99},
                        {'product_id': 2, 'quantity': 1, 'price': 19.99}
                    ],
                    'total': 79.97,
                    'item_count': 3
                }
            }

            response = requests.post(
                f"{self.server_url}/api/socketio/events/cart-update",
                json=cart_data,
                timeout=10
            )

            api_success = response.status_code == 200

            if api_success:
                time.sleep(2)  # Wait for update to be received

                # Check if any client received the cart update
                update_received = False
                for client in self.clients:
                    if any(event[0] == 'cart_update' for event in client.received_events):
                        update_received = True
                        break

                success = update_received
                details = f"API response: {response.status_code}, Update received: {update_received}"
            else:
                success = False
                details = f"API request failed: {response.status_code}"

        except Exception as e:
            success = False
            details = f"API cart update error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("API Cart Update Test", success, details, duration)
        return success

    def test_api_order_update(self) -> bool:
        """Test API endpoint for order status updates"""
        start_time = time.time()

        # Find a connected user
        target_user_id = None
        for user_data in self.test_data['users']:
            if user_data['user_id']:
                target_user_id = user_data['user_id']
                break

        if not target_user_id:
            self.log_test_result("API Order Update Test", False, "No user ID available", 0)
            return False

        try:
            # Clear previous events
            for client in self.clients:
                client.received_events.clear()

            # Send order update via API
            order_data = {
                'user_id': target_user_id,
                'order_id': self.test_data['orders'][0],
                'status': 'shipped'
            }

            response = requests.post(
                f"{self.server_url}/api/socketio/events/order-update",
                json=order_data,
                timeout=10
            )

            api_success = response.status_code == 200

            if api_success:
                time.sleep(2)  # Wait for update to be received

                # Check if any client received the order update
                update_received = False
                for client in self.clients:
                    if any(event[0] == 'order_status_update' for event in client.received_events):
                        update_received = True
                        break

                success = update_received
                details = f"API response: {response.status_code}, Update received: {update_received}"
            else:
                success = False
                details = f"API request failed: {response.status_code}"

        except Exception as e:
            success = False
            details = f"API order update error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("API Order Update Test", success, details, duration)
        return success

    def test_concurrent_connections(self, num_connections: int = 10) -> bool:
        """Test multiple concurrent connections"""
        start_time = time.time()

        try:
            concurrent_clients = []
            successful_connections = 0

            def connect_client(client_id):
                try:
                    client = self.create_test_client({'user_id': f'concurrent_user_{client_id}', 'is_admin': False})
                    auth_data = {'user_id': f'concurrent_user_{client_id}', 'is_admin': False}
                    client.connect(self.server_url, auth=auth_data, wait_timeout=15)

                    if client.connected:
                        return client
                    else:
                        return None
                except Exception as e:
                    logger.error(f"Concurrent connection {client_id} failed: {e}")
                    return None

            # Create connections concurrently
            with ThreadPoolExecutor(max_workers=num_connections) as executor:
                futures = [executor.submit(connect_client, i) for i in range(num_connections)]

                for future in as_completed(futures):
                    client = future.result()
                    if client:
                        concurrent_clients.append(client)
                        successful_connections += 1

            # Test if all clients can send/receive messages
            if concurrent_clients:
                # Send a ping from each client
                for client in concurrent_clients:
                    try:
                        client.emit('ping')
                    except Exception as e:
                        logger.error(f"Failed to send ping from concurrent client: {e}")

                time.sleep(3)  # Wait for responses

                # Check responses
                responses_received = 0
                for client in concurrent_clients:
                    if any(event[0] == 'pong' for event in client.received_events):
                        responses_received += 1

            # Clean up
            for client in concurrent_clients:
                try:
                    client.disconnect()
                except:
                    pass

            success = successful_connections >= num_connections * 0.8  # 80% success rate
            details = f"{successful_connections}/{num_connections} concurrent connections successful"
            if concurrent_clients:
                details += f", {responses_received}/{len(concurrent_clients)} responses received"

        except Exception as e:
            success = False
            details = f"Concurrent connection test error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Concurrent Connections Test", success, details, duration)
        return success

    def test_error_handling(self) -> bool:
        """Test error handling and edge cases"""
        start_time = time.time()

        if not self.clients:
            self.log_test_result("Error Handling Test", False, "No connected clients", 0)
            return False

        try:
            client = self.clients[0]
            error_tests_passed = 0
            total_error_tests = 0

            # Test 1: Send invalid event data
            try:
                client.emit('subscribe', {'invalid': 'data'})
                time.sleep(1)
                error_tests_passed += 1  # Should not crash
            except Exception as e:
                logger.error(f"Invalid event data test failed: {e}")
            total_error_tests += 1

            # Test 2: Send malformed JSON-like data
            try:
                client.emit('ping', {'malformed': {'nested': {'data': None}}})
                time.sleep(1)
                error_tests_passed += 1  # Should not crash
            except Exception as e:
                logger.error(f"Malformed data test failed: {e}")
            total_error_tests += 1

            # Test 3: Send very large data
            try:
                large_data = {'data': 'x' * 10000}  # 10KB of data
                client.emit('heartbeat', large_data)
                time.sleep(1)
                error_tests_passed += 1  # Should handle gracefully
            except Exception as e:
                logger.error(f"Large data test failed: {e}")
            total_error_tests += 1

            success = error_tests_passed == total_error_tests
            details = f"{error_tests_passed}/{total_error_tests} error handling tests passed"

        except Exception as e:
            success = False
            details = f"Error handling test error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Error Handling Test", success, details, duration)
        return success

    def test_reconnection(self) -> bool:
        """Test automatic reconnection functionality"""
        start_time = time.time()

        try:
            # Create a new client for reconnection testing
            client = self.create_test_client({'user_id': 'reconnect_test_user', 'is_admin': False})
            auth_data = {'user_id': 'reconnect_test_user', 'is_admin': False}

            # Initial connection
            client.connect(self.server_url, auth=auth_data, wait_timeout=10)

            if not client.connected:
                success = False
                details = "Initial connection failed"
            else:
                # Disconnect and try to reconnect
                client.disconnect()
                time.sleep(2)

                # Reconnect
                client.connect(self.server_url, auth=auth_data, wait_timeout=10)

                success = client.connected
                details = "Reconnection successful" if success else "Reconnection failed"

                if success:
                    client.disconnect()

        except Exception as e:
            success = False
            details = f"Reconnection test error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Reconnection Test", success, details, duration)
        return success

    def test_admin_features(self) -> bool:
        """Test admin-specific features"""
        start_time = time.time()

        # Find admin client
        admin_client = None
        for client in self.clients:
            # Check if this is an admin client by looking at test data
            for user_data in self.test_data['users']:
                if user_data['user_id'] == 'admin_user' and user_data['is_admin'] and client.connected:
                    admin_client = client
                    break
            if admin_client:
                break

        if not admin_client:
            self.log_test_result("Admin Features Test", False, "No admin client connected", 0)
            return False

        try:
            # Clear previous events
            admin_client.received_events.clear()

            # Test admin notification via API
            admin_notification_data = {
                'title': 'Admin Alert',
                'message': 'This is an admin-only notification',
                'type': 'admin',
                'priority': 'high'
            }

            response = requests.post(
                f"{self.server_url}/api/socketio/admin-notification",
                json=admin_notification_data,
                timeout=10
            )

            api_success = response.status_code == 200

            if api_success:
                time.sleep(2)  # Wait for notification

                # Check if admin received the notification
                admin_notification_received = any(
                    event[0] == 'admin_notification' for event in admin_client.received_events
                )

                success = admin_notification_received
                details = f"API response: {response.status_code}, Admin notification received: {admin_notification_received}"
            else:
                success = False
                details = f"Admin notification API failed: {response.status_code}"

        except Exception as e:
            success = False
            details = f"Admin features test error: {str(e)}"

        duration = time.time() - start_time
        self.log_test_result("Admin Features Test", success, details, duration)
        return success

    def cleanup_connections(self):
        """Clean up all test connections"""
        logger.info("🧹 Cleaning up test connections...")

        for client in self.clients:
            try:
                if client.connected:
                    client.disconnect()
            except Exception as e:
                logger.error(f"Error disconnecting client: {e}")

        self.clients.clear()
        logger.info("✅ All test connections cleaned up")

    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests

        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        report = {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': f"{success_rate:.1f}%",
                'total_duration': sum(result['duration'] for result in self.test_results.values())
            },
            'connection_stats': self.connection_stats,
            'test_results': self.test_results,
            'events_received': len(self.received_events),
            'timestamp': datetime.now().isoformat()
        }

        return report

    def run_all_tests(self) -> bool:
        """Run all SocketIO tests in sequence"""
        logger.info("🚀 Starting Comprehensive SocketIO Test Suite")
        logger.info("=" * 60)

        start_time = time.time()

        try:
            # Phase 1: Basic connectivity tests
            logger.info("📡 Phase 1: Basic Connectivity Tests")
            logger.info("-" * 40)

            if not self.test_server_availability():
                logger.error("❌ Server not available - aborting tests")
                return False

            self.test_socketio_endpoint_availability()

            # Phase 2: Connection tests
            logger.info("\n🔌 Phase 2: Connection Tests")
            logger.info("-" * 40)

            if not self.test_basic_connection():
                logger.error("❌ Basic connection failed - aborting tests")
                return False

            if not self.test_authenticated_connections():
                logger.error("❌ Authenticated connections failed - aborting tests")
                return False

            # Phase 3: Event handling tests
            logger.info("\n📨 Phase 3: Event Handling Tests")
            logger.info("-" * 40)

            self.test_ping_pong()
            self.test_subscription_events()
            self.test_heartbeat()

            # Phase 4: API integration tests
            logger.info("\n🌐 Phase 4: API Integration Tests")
            logger.info("-" * 40)

            self.test_api_broadcast()
            self.test_api_user_notification()
            self.test_api_inventory_update()
            self.test_api_cart_update()
            self.test_api_order_update()

            # Phase 5: Advanced features tests
            logger.info("\n⚡ Phase 5: Advanced Features Tests")
            logger.info("-" * 40)

            self.test_admin_features()
            self.test_error_handling()
            self.test_reconnection()

            # Phase 6: Performance tests
            logger.info("\n🏃 Phase 6: Performance Tests")
            logger.info("-" * 40)

            self.test_concurrent_connections(10)

            # Generate final report
            total_duration = time.time() - start_time
            logger.info(f"\n⏱️  Total test duration: {total_duration:.2f} seconds")

            return True

        except KeyboardInterrupt:
            logger.info("\n🛑 Tests interrupted by user")
            return False
        except Exception as e:
            logger.error(f"\n❌ Test suite error: {e}")
            return False
        finally:
            self.cleanup_connections()

    def print_final_report(self):
        """Print comprehensive final test report"""
        report = self.generate_test_report()

        logger.info("\n" + "=" * 60)
        logger.info("📊 COMPREHENSIVE SOCKETIO TEST REPORT")
        logger.info("=" * 60)

        # Summary
        summary = report['summary']
        logger.info(f"📈 Total Tests: {summary['total_tests']}")
        logger.info(f"✅ Passed: {summary['passed_tests']}")
        logger.info(f"❌ Failed: {summary['failed_tests']}")
        logger.info(f"📊 Success Rate: {summary['success_rate']}")
        logger.info(f"⏱️  Total Duration: {summary['total_duration']:.2f}s")

        # Connection stats
        stats = report['connection_stats']
        logger.info(f"\n🔌 Connection Statistics:")
        logger.info(f"   Successful: {stats['successful_connections']}")
        logger.info(f"   Failed: {stats['failed_connections']}")
        logger.info(f"   Disconnections: {stats['disconnections']}")
        logger.info(f"   Reconnections: {stats['reconnections']}")

        # Events
        logger.info(f"\n📨 Events Received: {report['events_received']}")

        # Failed tests details
        failed_tests = [name for name, result in report['test_results'].items() if not result['success']]
        if failed_tests:
            logger.info(f"\n❌ Failed Tests:")
            for test_name in failed_tests:
                result = report['test_results'][test_name]
                logger.info(f"   • {test_name}: {result['details']}")

        # Save detailed report to file
        try:
            with open('socketio_test_report.json', 'w') as f:
                json.dump(report, f, indent=2)
            logger.info(f"\n💾 Detailed report saved to: socketio_test_report.json")
        except Exception as e:
            logger.error(f"Failed to save report: {e}")

        logger.info("=" * 60)


def main():
    """Main test execution function"""
    print("🧪 Allora SocketIO Comprehensive Test Suite")
    print("=" * 50)

    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n🛑 Test interrupted by user")
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    # Get server URL from environment or use default
    server_url = os.getenv('SOCKETIO_TEST_SERVER', 'http://localhost:5000')

    print(f"🎯 Target Server: {server_url}")
    print(f"📅 Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)

    # Create test suite
    test_suite = SocketIOTestSuite(server_url)

    try:
        # Run all tests
        success = test_suite.run_all_tests()

        # Print final report
        test_suite.print_final_report()

        # Exit with appropriate code
        exit_code = 0 if success else 1

        if success:
            print("\n🎉 All tests completed successfully!")
        else:
            print("\n⚠️  Some tests failed. Check the report above.")

        sys.exit(exit_code)

    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        test_suite.cleanup_connections()
        sys.exit(1)


if __name__ == "__main__":
    main()
