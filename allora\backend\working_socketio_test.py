#!/usr/bin/env python3
"""
Working SocketIO Test
====================

A test that properly handles SocketIO connection timeouts and uses WebSocket transport.
"""

import socketio
import time
import requests
import threading
from datetime import datetime

def test_server_health():
    """Test if the server is running"""
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=10)
        print(f"✅ Server Health: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Server Health Failed: {e}")
        return False

def test_websocket_connection():
    """Test SocketIO connection using WebSocket transport"""
    print("🔌 Testing SocketIO WebSocket Connection...")
    
    # Create client that prefers WebSocket transport
    sio = socketio.Client(
        logger=False, 
        engineio_logger=False,
        request_timeout=60,  # Increase timeout to 60 seconds
        reconnection=True,
        reconnection_attempts=3,
        reconnection_delay=1
    )
    
    connected = False
    connection_error = None
    events_received = []
    
    @sio.event
    def connect():
        nonlocal connected
        connected = True
        print("✅ Connected to SocketIO server!")
    
    @sio.event
    def disconnect():
        print("🔌 Disconnected from SocketIO server")
    
    @sio.event
    def connect_error(data):
        nonlocal connection_error
        connection_error = data
        print(f"❌ Connection error: {data}")
    
    @sio.event
    def pong(data):
        events_received.append(('pong', data))
        print(f"✅ Pong received: {data}")
    
    @sio.event
    def connection_established(data):
        events_received.append(('connection_established', data))
        print(f"✅ Connection established: {data}")
    
    @sio.event
    def subscribed(data):
        events_received.append(('subscribed', data))
        print(f"✅ Subscription confirmed: {data}")
    
    @sio.event
    def heartbeat_ack(data):
        events_received.append(('heartbeat_ack', data))
        print(f"✅ Heartbeat acknowledged: {data}")
    
    @sio.event
    def broadcast(data):
        events_received.append(('broadcast', data))
        print(f"✅ Broadcast received: {data}")
    
    @sio.event
    def notification(data):
        events_received.append(('notification', data))
        print(f"✅ Notification received: {data}")
    
    try:
        # Try to connect with WebSocket transport preference
        print("Attempting WebSocket connection...")
        sio.connect(
            'http://localhost:5000',
            transports=['websocket', 'polling'],  # Prefer WebSocket
            wait_timeout=30
        )
        
        # Wait for connection to establish
        time.sleep(2)
        
        if connected:
            print("✅ SocketIO WebSocket connection successful!")
            
            # Test ping-pong
            print("🏓 Testing ping-pong...")
            sio.emit('ping')
            time.sleep(3)
            
            # Test heartbeat
            print("💓 Testing heartbeat...")
            sio.emit('heartbeat')
            time.sleep(3)
            
            # Test subscription
            print("🔔 Testing subscription...")
            sio.emit('subscribe', {'events': ['inventory', 'prices', 'orders']})
            time.sleep(3)
            
            # Test custom event
            print("📨 Testing custom event...")
            sio.emit('test_event', {'message': 'Hello from test client!'})
            time.sleep(2)
            
            print(f"\n📊 Events received: {len(events_received)}")
            for event_name, event_data in events_received:
                print(f"   - {event_name}: {event_data}")
            
            # Disconnect
            sio.disconnect()
            return True
        else:
            print(f"❌ Failed to connect. Error: {connection_error}")
            return False
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        try:
            sio.disconnect()
        except:
            pass
        return False

def test_api_integration():
    """Test SocketIO API endpoints with a connected client"""
    print("🌐 Testing SocketIO API Integration...")
    
    # Create a client to receive events
    sio = socketio.Client(
        logger=False, 
        engineio_logger=False,
        request_timeout=60
    )
    
    events_received = []
    connected = False
    
    @sio.event
    def connect():
        nonlocal connected
        connected = True
        print("✅ Test client connected for API testing")
    
    @sio.event
    def broadcast(data):
        events_received.append(('broadcast', data))
        print(f"✅ API Broadcast received: {data}")
    
    @sio.event
    def notification(data):
        events_received.append(('notification', data))
        print(f"✅ API Notification received: {data}")
    
    @sio.event
    def inventory_update(data):
        events_received.append(('inventory_update', data))
        print(f"✅ API Inventory update received: {data}")
    
    try:
        # Connect client
        sio.connect('http://localhost:5000', transports=['websocket', 'polling'], wait_timeout=30)
        time.sleep(2)
        
        if not connected:
            print("❌ Failed to connect test client for API testing")
            return False
        
        # Test broadcast API
        print("📢 Testing broadcast API...")
        response = requests.post(
            'http://localhost:5000/api/socketio/broadcast',
            json={'message': 'Test broadcast from API', 'type': 'test'},
            timeout=10
        )
        print(f"   API Response: {response.status_code}")
        time.sleep(2)
        
        # Test user notification API
        print("🔔 Testing user notification API...")
        response = requests.post(
            'http://localhost:5000/api/socketio/notify-user/test_user',
            json={'title': 'Test Notification', 'message': 'Test from API'},
            timeout=10
        )
        print(f"   API Response: {response.status_code}")
        time.sleep(2)
        
        # Test inventory update API
        print("📦 Testing inventory update API...")
        response = requests.post(
            'http://localhost:5000/api/socketio/events/inventory-update',
            json={'product_id': 123, 'new_quantity': 50, 'old_quantity': 30},
            timeout=10
        )
        print(f"   API Response: {response.status_code}")
        time.sleep(2)
        
        print(f"\n📊 API Events received: {len(events_received)}")
        for event_name, event_data in events_received:
            print(f"   - {event_name}: {event_data}")
        
        sio.disconnect()
        return len(events_received) > 0
        
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        try:
            sio.disconnect()
        except:
            pass
        return False

def test_concurrent_connections():
    """Test multiple concurrent connections"""
    print("🔀 Testing concurrent connections...")
    
    clients = []
    connected_count = 0
    
    def create_client(client_id):
        nonlocal connected_count
        try:
            client = socketio.Client(logger=False, engineio_logger=False, request_timeout=60)
            
            @client.event
            def connect():
                nonlocal connected_count
                connected_count += 1
                print(f"   Client {client_id} connected")
            
            client.connect('http://localhost:5000', transports=['websocket', 'polling'], wait_timeout=30)
            clients.append(client)
            return True
        except Exception as e:
            print(f"   Client {client_id} failed: {e}")
            return False
    
    # Create 5 concurrent connections
    threads = []
    for i in range(5):
        thread = threading.Thread(target=create_client, args=(i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    time.sleep(3)  # Wait for connections to establish
    
    print(f"✅ {connected_count}/5 concurrent connections successful")
    
    # Clean up
    for client in clients:
        try:
            client.disconnect()
        except:
            pass
    
    return connected_count >= 3  # At least 3 out of 5 should succeed

def main():
    """Run all working tests"""
    print("🧪 Working SocketIO Test Suite")
    print("=" * 50)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    # Test 1: Server health
    if not test_server_health():
        print("❌ Server not available - aborting tests")
        return
    
    # Test 2: WebSocket connection
    websocket_success = test_websocket_connection()
    
    # Test 3: API integration
    api_success = test_api_integration()
    
    # Test 4: Concurrent connections
    concurrent_success = test_concurrent_connections()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    print(f"WebSocket Connection: {'✅ PASS' if websocket_success else '❌ FAIL'}")
    print(f"API Integration: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"Concurrent Connections: {'✅ PASS' if concurrent_success else '❌ FAIL'}")
    
    overall_success = websocket_success and api_success and concurrent_success
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if overall_success else '⚠️ SOME TESTS FAILED'}")
    print("=" * 50)

if __name__ == "__main__":
    main()
